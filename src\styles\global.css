@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Theme Support */
:root {
  /* Light theme colors */
  --color-bg-primary: theme('colors.background.light');
  --color-bg-secondary: theme('colors.background.light-secondary');
  --color-text-primary: theme('colors.text.light');
  --color-text-secondary: theme('colors.text.light-secondary');
  --color-border: theme('colors.secondary.200');
  --color-shadow: rgba(0, 0, 0, 0.1);

  /* Glassmorphism variables */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark {
  /* Dark theme colors (following 2024 best practices - avoiding pure black) */
  --color-bg-primary: theme('colors.background.dark');
  --color-bg-secondary: theme('colors.background.dark-secondary');
  --color-text-primary: theme('colors.text.dark');
  --color-text-secondary: theme('colors.text.dark-secondary');
  --color-border: theme('colors.secondary.600');
  --color-shadow: rgba(0, 0, 0, 0.4);

  /* Dark mode glassmorphism - improved for new dark colors */
  --glass-bg: rgba(18, 18, 18, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--color-text-primary);
  line-height: 1.6;
  background-color: var(--color-bg-primary);
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* Optimize font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-variant-ligatures: common-ligatures;
}

main {
  flex-grow: 1;
}

/* Improved focus styles for accessibility */
*:focus {
  outline: 2px solid theme('colors.primary.500');
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid theme('colors.primary.500');
  outline-offset: 2px;
}

/* Performance optimizations */
img {
  height: auto;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

img[loading="lazy"] {
  transition: opacity 0.3s ease-in-out;
}

/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Component Styles */
@layer components {
  /* Modern glassmorphism card */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: theme('borderRadius.2xl');
  }

  /* Modern button styles */
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl;
    @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white;
    @apply shadow-medium hover:shadow-large transition-all duration-300;
    @apply hover:-translate-y-0.5 hover:shadow-glow;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl;
    @apply bg-white text-secondary-700 border border-secondary-300;
    @apply shadow-soft hover:shadow-medium transition-all duration-300;
    @apply hover:-translate-y-0.5 hover:bg-secondary-50;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply dark:bg-secondary-800 dark:text-secondary-200 dark:border-secondary-600;
    @apply dark:hover:bg-secondary-700;
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl;
    @apply text-secondary-700 hover:bg-secondary-100 transition-all duration-300;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
    @apply dark:text-secondary-300 dark:hover:bg-secondary-800;
  }

  /* Modern card styles */
  .card {
    @apply bg-white rounded-2xl shadow-soft border border-secondary-200;
    @apply transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
    @apply dark:bg-secondary-800 dark:border-secondary-700;
  }

  .card-interactive {
    @apply card cursor-pointer;
    @apply hover:shadow-large hover:-translate-y-2;
    @apply hover:border-primary-300 dark:hover:border-primary-600;
  }

  /* Typography improvements */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 via-primary-500 to-accent-500;
    @apply bg-clip-text text-transparent;
  }

  .heading-xl {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold font-heading leading-tight;
    @apply text-gradient;
  }

  .heading-lg {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold font-heading leading-tight;
  }

  .heading-md {
    @apply text-2xl sm:text-3xl font-semibold font-heading leading-tight;
  }

  .heading-sm {
    @apply text-xl sm:text-2xl font-semibold font-heading leading-tight;
  }

  /* Navigation styles */
  .nav-link {
    @apply relative px-3 py-2 text-sm font-medium transition-all duration-300;
    @apply text-secondary-600 hover:text-primary-600;
    @apply dark:text-secondary-300 dark:hover:text-primary-400;
  }

  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-0.5;
    @apply bg-gradient-to-r from-primary-500 to-accent-500;
    @apply transform scale-x-0 transition-transform duration-300 origin-right;
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    @apply scale-x-100 origin-left;
  }

  /* Section spacing */
  .section {
    @apply py-16 lg:py-24;
  }

  .section-sm {
    @apply py-12 lg:py-16;
  }

  /* Container improvements */
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
}

/* Utility Classes */
@layer utilities {
  /* Theme transition utilities */
  .theme-transition {
    transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  /* Glassmorphism utilities */
  .backdrop-blur-glass {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  /* Animation utilities */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }

  /* Text selection */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Modern shadows */
  .shadow-glow-primary {
    box-shadow: 0 0 20px theme('colors.primary.500/15');
  }

  .shadow-glow-accent {
    box-shadow: 0 0 20px theme('colors.accent.500/15');
  }
}